@layer legacy {
    .typography {
        --apl-typography-color-default: var(--apl-colors-content-default);
        --apl-typography-color-error: var(--apl-colors-content-danger-default);
        --apl-typography-color-warning: var(--apl-colors-content-warning-default);
        --apl-typography-color-success: var(--apl-colors-content-success-default);
        --apl-typography-color-process: var(--apl-colors-content-process-default);
        --apl-typography-color-primary: var(--apl-colors-content-primary-default);
    }

}

@layer apollo {
    .typography {
        --apl-typography-color-default: var(--apl-alias-color-background-and-surface-on-surface);
        --apl-typography-color-error: var(--apl-alias-color-error-error);
        --apl-typography-color-warning: var(--apl-alias-color-warning-warning);
        --apl-typography-color-success: var(--apl-alias-color-success-success);
        --apl-typography-color-primary: var(--apl-alias-color-primary-primary);
        --apl-typography-color-secondary: var(--apl-alias-color-secondary-secondary);
        --apl-typography-color-tertiary: var(--apl-alias-color-tertiary-tertiary);
    }

}

.root {
    margin: 0;
}

.gutterBottom {
    margin-bottom: 0.35em;
}

.noWrap {
    white-space: nowrap;
}

.alignLeft {
    text-align: left;
}

.alignCenter {
    text-align: center;
}

.alignRight {
    text-align: right;
}

.alignJustify {
    text-align: justify;
}

/* Color classes */
.colorDefault {
    color: var(--apl-typography-color-default);
}

.colorNegative {
    color: var(--apl-typography-color-error);
}

.colorWarning {
    color: var(--apl-typography-color-warning);
}

.colorSuccess {
    color: var(--apl-typography-color-success);
}

.colorProcess {
    color: var(--apl-typography-color-process);
}

.colorPrimary {
    color: var(--apl-typography-color-primary);
}

.colorSecondary {
    color: var(--apl-typography-color-secondary);
}

.colorTertiary {
    color: var(--apl-typography-color-tertiary);
}

/* Typography level classes */
.aplTypographyDisplay1 {
    composes: apl-typography-display1 from '../../base.module.css';
}

.aplTypographyDisplay2 {
    composes: apl-typography-display2 from '../../base.module.css';
}

.aplTypographyH1 {
    composes: apl-typography-h1 from '../../base.module.css';
}

.aplTypographyH2 {
    composes: apl-typography-h2 from '../../base.module.css';
}

.aplTypographyH3 {
    composes: apl-typography-h3 from '../../base.module.css';
}

.aplTypographyH4 {
    composes: apl-typography-h4 from '../../base.module.css';
}

.aplTypographyH5 {
    composes: apl-typography-h5 from '../../base.module.css';
}

.aplTypographyBody1 {
    composes: apl-typography-body1 from '../../base.module.css';
}

.aplTypographyBody2 {
    composes: apl-typography-body2 from '../../base.module.css';
}

.aplTypographyCaption {
    composes: apl-typography-caption from '../../base.module.css';
}

.aplTypographyTextlink {
    composes: apl-typography-textlink from '../../base.module.css';
}

/* M3 Typography */

.aplTypographyDisplayLarge {
    composes: apl-typography-display-large from '../../base.module.css';
}

.aplTypographyDisplayMedium {
    composes: apl-typography-display-medium from '../../base.module.css';
}

.aplTypographyDisplaySmall {
    composes: apl-typography-display-small from '../../base.module.css';
}

.aplTypographyHeadlineLarge {
    composes: apl-typography-headline-large from '../../base.module.css';
}

.aplTypographyHeadlineMedium {
    composes: apl-typography-headline-medium from '../../base.module.css';
}

.aplTypographyHeadlineSmall {
    composes: apl-typography-headline-small from '../../base.module.css';
}

.aplTypographyTitleLarge {
    composes: apl-typography-title-large from '../../base.module.css';
}

.aplTypographyTitleMedium {
    composes: apl-typography-title-medium from '../../base.module.css';
}

.aplTypographyTitleSmall {
    composes: apl-typography-title-small from '../../base.module.css';
}

.aplTypographyBodyLarge {
    composes: apl-typography-body-large from '../../base.module.css';
}

.aplTypographyBodyLargeEmphasized {
    composes: apl-typography-body-large-emphasized from '../../base.module.css';
}

.aplTypographyBodyMedium {
    composes: apl-typography-body-medium from '../../base.module.css';
}

.aplTypographyBodyMediumEmphasized {
    composes: apl-typography-body-medium-emphasized from '../../base.module.css';
}

.aplTypographyBodySmall {
    composes: apl-typography-body-small from '../../base.module.css';
}

.aplTypographyBodySmallEmphasized {
    composes: apl-typography-body-small-emphasized from '../../base.module.css';
}

.aplTypographyLabelLarge {
    composes: apl-typography-label-large from '../../base.module.css';
}

.aplTypographyLabelLargeEmphasized {
    composes: apl-typography-label-large-emphasized from '../../base.module.css';
}

.aplTypographyLabelMedium {
    composes: apl-typography-label-medium from '../../base.module.css';
}

.aplTypographyLabelMediumEmphasized {
    composes: apl-typography-label-medium-emphasized from '../../base.module.css';
}

.aplTypographyLabelSmall {
    composes: apl-typography-label-small from '../../base.module.css';
}

.aplTypographyLabelSmallEmphasized {
    composes: apl-typography-label-small-emphasized from '../../base.module.css';
}
