import type {
  AnchorHTMLAttributes,
  HTMLAttributes,
  PropsWithChildren,
  Ref,
} from "react"

export type TypographyVariant =
  | "display1"
  | "display2"
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "body1"
  | "body2"
  | "caption"
  | "textlink"
  // m3
  | "displayLarge"
  | "displayMedium"
  | "displaySmall"
  | "headlineLarge"
  | "headlineMedium"
  | "headlineSmall"
  | "titleLarge"
  | "titleMedium"
  | "titleSmall"
  | "bodyLarge"
  | "bodyLargeEmphasized"
  | "bodyMedium"
  | "bodyMediumEmphasized"
  | "bodySmall"
  | "bodySmallEmphasized"
  | "labelLarge"
  | "labelLargeEmphasized"
  | "labelMedium"
  | "labelMediumEmphasized"
  | "labelSmall"
  | "labelSmallEmphasized"

export type TypographyBaseProps = PropsWithChildren<{
  level?: TypographyVariant
  align?: "left" | "center" | "right" | "justify"
  color?: "default" | "negative" | "warning" | "success" | "process" | "primary"
  gutterBottom?: boolean
  noWrap?: boolean
  emphasize?: boolean
}>

export type TextLinkProps = {
  ref?: Ref<HTMLAnchorElement>
} & AnchorHTMLAttributes<HTMLAnchorElement>

export type HeadingProps = {
  ref?: Ref<HTMLHeadingElement>
} & HTMLAttributes<HTMLHeadingElement>

export type ParagraphProps = {
  ref?: Ref<HTMLParagraphElement>
} & HTMLAttributes<HTMLParagraphElement>

export type SpanProps = {
  ref?: Ref<HTMLSpanElement>
} & HTMLAttributes<HTMLSpanElement>

export type TypographyProps = (
  | ({ level?: "textlink" } & TextLinkProps)
  | ({
      level?:
        | "display1"
        | "display2"
        | "h1"
        | "h2"
        | "h3"
        | "h4"
        | "h5"
        | "displayLarge"
        | "displayMedium"
        | "displaySmall"
        | "headlineLarge"
        | "headlineMedium"
        | "headlineSmall"
        | "titleLarge"
        | "titleMedium"
        | "titleSmall"
    } & HeadingProps)
  | ({
      level?:
        | "body1"
        | "body2"
        | "bodyLarge"
        | "bodyMedium"
        | "bodySmall"
        | "bodyLargeEmphasized"
        | "bodyMediumEmphasized"
        | "bodySmallEmphasized"
    } & ParagraphProps)
  | ({
      level?:
        | "caption"
        | "labelLarge"
        | "labelMedium"
        | "labelSmall"
        | "labelLargeEmphasized"
        | "labelMediumEmphasized"
        | "labelSmallEmphasized"
    } & SpanProps)
) &
  TypographyBaseProps
