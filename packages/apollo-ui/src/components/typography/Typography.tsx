import React from "react"
import classNames from "classnames"

import styles from "./typography.module.css"
import { TypographyProps, TypographyVariant } from "./TypographyProps"

const defaultLevelMapping: Record<TypographyVariant, string> = {
  display1: "h1",
  display2: "h1",
  h1: "h1",
  h2: "h2",
  h3: "h3",
  h4: "h4",
  h5: "h5",
  body1: "p",
  body2: "p",
  caption: "span",
  textlink: "a",
  // m3
  displayLarge: "h1",
  displayMedium: "h1",
  displaySmall: "h1",
  headlineLarge: "h1",
  headlineMedium: "h2",
  headlineSmall: "h2",
  titleLarge: "h3",
  titleMedium: "h3",
  titleSmall: "h4",
  bodyLarge: "p",
  bodyLargeEmphasized: "p",
  bodyMedium: "p",
  bodyMediumEmphasized: "p",
  bodySmall: "p",
  bodySmallEmphasized: "p",
  labelLarge: "span",
  labelLargeEmphasized: "span",
  labelMedium: "span",
  labelMediumEmphasized: "span",
  labelSmall: "span",
  labelSmallEmphasized: "span",
}

const alignmentMap: Record<string, string> = {
  left: styles.alignLeft,
  center: styles.alignCenter,
  right: styles.alignRight,
  justify: styles.alignJustify,
}

const colorMap: Record<string, string> = {
  default: styles.colorDefault,
  negative: styles.colorNegative, //apollo (m3)
  warning: styles.colorWarning,
  success: styles.colorSuccess,
  process: styles.colorProcess,
  primary: styles.colorPrimary,
}

const levelMap: Record<TypographyVariant, string> = {
  display1: styles.aplTypographyDisplay1,
  display2: styles.aplTypographyDisplay2,
  h1: styles.aplTypographyH1,
  h2: styles.aplTypographyH2,
  h3: styles.aplTypographyH3,
  h4: styles.aplTypographyH4,
  h5: styles.aplTypographyH5,
  body1: styles.aplTypographyBody1,
  body2: styles.aplTypographyBody2,
  caption: styles.aplTypographyCaption,
  textlink: styles.aplTypographyTextlink,
  // m3
  displayLarge: styles.aplTypographyDisplayLarge,
  displayMedium: styles.aplTypographyDisplayMedium,
  displaySmall: styles.aplTypographyDisplaySmall,
  headlineLarge: styles.aplTypographyHeadlineLarge,
  headlineMedium: styles.aplTypographyHeadlineMedium,
  headlineSmall: styles.aplTypographyHeadlineSmall,
  titleLarge: styles.aplTypographyTitleLarge,
  titleMedium: styles.aplTypographyTitleMedium,
  titleSmall: styles.aplTypographyTitleSmall,
  bodyLarge: styles.aplTypographyBodyLarge,
  bodyLargeEmphasized: styles.aplTypographyBodyLargeEmphasized,
  bodyMedium: styles.aplTypographyBodyMedium,
  bodyMediumEmphasized: styles.aplTypographyBodyMediumEmphasized,
  bodySmall: styles.aplTypographyBodySmall,
  bodySmallEmphasized: styles.aplTypographyBodySmallEmphasized,
  labelLarge: styles.aplTypographyLabelLarge,
  labelLargeEmphasized: styles.aplTypographyLabelLargeEmphasized,
  labelMedium: styles.aplTypographyLabelMedium,
  labelMediumEmphasized: styles.aplTypographyLabelMediumEmphasized,
  labelSmall: styles.aplTypographyLabelSmall,
  labelSmallEmphasized: styles.aplTypographyLabelSmallEmphasized,
}

export function Typography({
  level = "bodyLarge",
  align = "left",
  color,
  gutterBottom,
  noWrap,
  className,
  children,
  ...rest
}: TypographyProps) {
  const Root = (defaultLevelMapping?.[level] ?? "span") as React.ElementType
  const alignmentClass = alignmentMap[align]
  const colorClass =
    level !== "textlink"
      ? color
        ? colorMap[color]
        : undefined
      : colorMap[color ?? "process"]
  const levelClass = levelMap[level]

  return (
    <Root
      className={classNames(
        "ApolloTypography--root",
        styles.typography,
        styles.root,
        colorClass,
        alignmentClass,
        levelClass,
        className,
        {
          [styles.gutterBottom]: gutterBottom,
          [styles.noWrap]: noWrap,
        }
      )}
      {...rest}
    >
      {children}
    </Root>
  )
}
